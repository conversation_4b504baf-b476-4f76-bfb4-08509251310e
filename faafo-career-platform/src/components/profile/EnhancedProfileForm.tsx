'use client';

import { useState, useEffect, FormEvent } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { X, Plus, Save, AlertCircle, CheckCircle, Shield, Eye, EyeOff } from 'lucide-react';
import PhotoUpload from './PhotoUpload';

interface ProfileData {
  id?: string;
  bio?: string;
  profilePictureUrl?: string;
  socialMediaLinks?: { [key: string]: string };
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  company?: string;
  location?: string;
  phoneNumber?: string;
  website?: string;
  careerInterests?: string[];
  skillsToLearn?: string[];
  experienceLevel?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  currentIndustry?: string;
  targetIndustry?: string;
  profileCompletionScore?: number;
  lastProfileUpdate?: string;
  weeklyLearningGoal?: number;
  emailNotifications?: boolean;
  profileVisibility?: 'PRIVATE' | 'PUBLIC' | 'COMMUNITY_ONLY';
}

interface EnhancedProfileFormProps {
  initialData: ProfileData;
  onSave: (data: ProfileData) => Promise<{ success: boolean; message?: string }>;
}

const CAREER_INTERESTS_OPTIONS = [
  'Entrepreneurship', 'Freelancing', 'Remote Work', 'Consulting', 'Creative Arts',
  'Technology', 'Healthcare', 'Education', 'Finance', 'Marketing', 'Sales',
  'Project Management', 'Data Science', 'Cybersecurity', 'Digital Marketing',
  'Content Creation', 'Coaching', 'Non-Profit', 'Real Estate', 'E-commerce'
];

const SKILLS_TO_LEARN_OPTIONS = [
  'Programming', 'Data Analysis', 'Digital Marketing', 'Project Management',
  'Graphic Design', 'Writing', 'Public Speaking', 'Leadership', 'Sales',
  'Financial Planning', 'Social Media', 'SEO', 'Photography', 'Video Editing',
  'Web Development', 'Mobile Development', 'Cloud Computing', 'AI/ML',
  'Blockchain', 'Cybersecurity', 'UX/UI Design', 'Business Strategy'
];

const INDUSTRIES = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Retail', 'Manufacturing',
  'Consulting', 'Media & Entertainment', 'Real Estate', 'Transportation',
  'Energy', 'Government', 'Non-Profit', 'Agriculture', 'Construction',
  'Hospitality', 'Legal', 'Marketing & Advertising', 'Telecommunications'
];

export default function EnhancedProfileForm({ initialData, onSave }: EnhancedProfileFormProps) {
  const [formData, setFormData] = useState<ProfileData>(initialData);
  const [formFeedback, setFormFeedback] = useState<{ type: 'success' | 'error'; content: string } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCareerInterest, setNewCareerInterest] = useState('');
  const [newSkillToLearn, setNewSkillToLearn] = useState('');

  useEffect(() => {
    setFormData(initialData);
    setFormFeedback(null);
  }, [initialData]);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSocialLinkChange = (platform: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      socialMediaLinks: { 
        ...(prev.socialMediaLinks || {}), 
        [platform]: value 
      }
    }));
  };

  const addCareerInterest = () => {
    if (newCareerInterest.trim() && !formData.careerInterests?.includes(newCareerInterest.trim())) {
      setFormData(prev => ({
        ...prev,
        careerInterests: [...(prev.careerInterests || []), newCareerInterest.trim()]
      }));
      setNewCareerInterest('');
    }
  };

  const removeCareerInterest = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      careerInterests: prev.careerInterests?.filter(i => i !== interest) || []
    }));
  };

  const addSkillToLearn = () => {
    if (newSkillToLearn.trim() && !formData.skillsToLearn?.includes(newSkillToLearn.trim())) {
      setFormData(prev => ({
        ...prev,
        skillsToLearn: [...(prev.skillsToLearn || []), newSkillToLearn.trim()]
      }));
      setNewSkillToLearn('');
    }
  };

  const removeSkillToLearn = (skill: string) => {
    setFormData(prev => ({
      ...prev,
      skillsToLearn: prev.skillsToLearn?.filter(s => s !== skill) || []
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setFormFeedback(null);
    setIsSubmitting(true);

    try {
      const result = await onSave(formData);
      if (result.success) {
        setFormFeedback({ type: 'success', content: result.message || 'Profile saved successfully!' });
      } else {
        setFormFeedback({ type: 'error', content: result.message || 'Failed to save profile.' });
      }
    } catch (error) {
      setFormFeedback({ type: 'error', content: 'An unexpected error occurred.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {formFeedback && (
        <div className={`p-4 rounded-md ${
          formFeedback.type === 'success' 
            ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200 border border-green-200 dark:border-green-800' 
            : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800'
        }`}>
          {formFeedback.content}
        </div>
      )}

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Basic information about yourself</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={formData.firstName || ''}
                onChange={(e) => handleChange('firstName', e.target.value)}
                placeholder="Enter your first name"
              />
            </div>
            <div>
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={formData.lastName || ''}
                onChange={(e) => handleChange('lastName', e.target.value)}
                placeholder="Enter your last name"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              value={formData.bio || ''}
              onChange={(e) => handleChange('bio', e.target.value)}
              placeholder="Tell us about yourself..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                value={formData.location || ''}
                onChange={(e) => handleChange('location', e.target.value)}
                placeholder="City, Country"
              />
            </div>
            <div>
              <Label htmlFor="phoneNumber">Phone Number</Label>
              <Input
                id="phoneNumber"
                value={formData.phoneNumber || ''}
                onChange={(e) => handleChange('phoneNumber', e.target.value)}
                placeholder="+****************"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="profilePictureUrl">Profile Picture URL</Label>
              <Input
                id="profilePictureUrl"
                type="url"
                value={formData.profilePictureUrl || ''}
                onChange={(e) => handleChange('profilePictureUrl', e.target.value)}
                placeholder="https://example.com/photo.jpg"
              />
            </div>
            <div>
              <Label htmlFor="website">Personal Website</Label>
              <Input
                id="website"
                type="url"
                value={formData.website || ''}
                onChange={(e) => handleChange('website', e.target.value)}
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Professional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Professional Information</CardTitle>
          <CardDescription>Your current and target career details</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="jobTitle">Current Job Title</Label>
              <Input
                id="jobTitle"
                value={formData.jobTitle || ''}
                onChange={(e) => handleChange('jobTitle', e.target.value)}
                placeholder="Software Engineer, Manager, etc."
              />
            </div>
            <div>
              <Label htmlFor="company">Current Company</Label>
              <Input
                id="company"
                value={formData.company || ''}
                onChange={(e) => handleChange('company', e.target.value)}
                placeholder="Company name"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="experienceLevel">Experience Level</Label>
              <Select value={formData.experienceLevel || ''} onValueChange={(value) => handleChange('experienceLevel', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BEGINNER">Beginner</SelectItem>
                  <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                  <SelectItem value="ADVANCED">Advanced</SelectItem>
                  <SelectItem value="EXPERT">Expert</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="currentIndustry">Current Industry</Label>
              <Select value={formData.currentIndustry || ''} onValueChange={(value) => handleChange('currentIndustry', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map(industry => (
                    <SelectItem key={industry} value={industry}>{industry}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="targetIndustry">Target Industry</Label>
              <Select value={formData.targetIndustry || ''} onValueChange={(value) => handleChange('targetIndustry', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select target" />
                </SelectTrigger>
                <SelectContent>
                  {INDUSTRIES.map(industry => (
                    <SelectItem key={industry} value={industry}>{industry}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Career Interests */}
      <Card>
        <CardHeader>
          <CardTitle>Career Interests</CardTitle>
          <CardDescription>Areas you're interested in exploring</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {formData.careerInterests?.map((interest) => (
              <Badge key={interest} variant="secondary" className="flex items-center gap-1">
                {interest}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-500"
                  onClick={() => removeCareerInterest(interest)}
                />
              </Badge>
            ))}
          </div>

          <div className="flex gap-2">
            <Select value={newCareerInterest} onValueChange={setNewCareerInterest}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select a career interest" />
              </SelectTrigger>
              <SelectContent>
                {CAREER_INTERESTS_OPTIONS.filter(option =>
                  !formData.careerInterests?.includes(option)
                ).map(option => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button type="button" onClick={addCareerInterest} disabled={!newCareerInterest}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Skills to Learn */}
      <Card>
        <CardHeader>
          <CardTitle>Skills to Learn</CardTitle>
          <CardDescription>Skills you want to develop</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2 mb-4">
            {formData.skillsToLearn?.map((skill) => (
              <Badge key={skill} variant="outline" className="flex items-center gap-1">
                {skill}
                <X
                  className="h-3 w-3 cursor-pointer hover:text-red-500"
                  onClick={() => removeSkillToLearn(skill)}
                />
              </Badge>
            ))}
          </div>

          <div className="flex gap-2">
            <Select value={newSkillToLearn} onValueChange={setNewSkillToLearn}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select a skill to learn" />
              </SelectTrigger>
              <SelectContent>
                {SKILLS_TO_LEARN_OPTIONS.filter(option =>
                  !formData.skillsToLearn?.includes(option)
                ).map(option => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button type="button" onClick={addSkillToLearn} disabled={!newSkillToLearn}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Social Media Links */}
      <Card>
        <CardHeader>
          <CardTitle>Social Media & Links</CardTitle>
          <CardDescription>Connect your professional profiles</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="linkedin">LinkedIn</Label>
              <Input
                id="linkedin"
                type="url"
                value={formData.socialMediaLinks?.linkedin || ''}
                onChange={(e) => handleSocialLinkChange('linkedin', e.target.value)}
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </div>
            <div>
              <Label htmlFor="twitter">Twitter</Label>
              <Input
                id="twitter"
                type="url"
                value={formData.socialMediaLinks?.twitter || ''}
                onChange={(e) => handleSocialLinkChange('twitter', e.target.value)}
                placeholder="https://twitter.com/yourusername"
              />
            </div>
            <div>
              <Label htmlFor="github">GitHub</Label>
              <Input
                id="github"
                type="url"
                value={formData.socialMediaLinks?.github || ''}
                onChange={(e) => handleSocialLinkChange('github', e.target.value)}
                placeholder="https://github.com/yourusername"
              />
            </div>
            <div>
              <Label htmlFor="portfolio">Portfolio</Label>
              <Input
                id="portfolio"
                type="url"
                value={formData.socialMediaLinks?.portfolio || ''}
                onChange={(e) => handleSocialLinkChange('portfolio', e.target.value)}
                placeholder="https://yourportfolio.com"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Preferences</CardTitle>
          <CardDescription>Customize your experience</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="weeklyLearningGoal">Weekly Learning Goal</Label>
              <Select
                value={formData.weeklyLearningGoal?.toString() || '3'}
                onValueChange={(value) => handleChange('weeklyLearningGoal', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select goal" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 resource per week</SelectItem>
                  <SelectItem value="2">2 resources per week</SelectItem>
                  <SelectItem value="3">3 resources per week</SelectItem>
                  <SelectItem value="5">5 resources per week</SelectItem>
                  <SelectItem value="7">7 resources per week</SelectItem>
                  <SelectItem value="10">10 resources per week</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="profileVisibility">Profile Visibility</Label>
              <Select
                value={formData.profileVisibility || 'PRIVATE'}
                onValueChange={(value) => handleChange('profileVisibility', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PRIVATE">Private</SelectItem>
                  <SelectItem value="COMMUNITY_ONLY">Community Only</SelectItem>
                  <SelectItem value="PUBLIC">Public</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="emailNotifications"
              checked={formData.emailNotifications !== false}
              onCheckedChange={(checked) => handleChange('emailNotifications', checked)}
            />
            <Label htmlFor="emailNotifications">
              Receive email notifications for updates and recommendations
            </Label>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save Profile'}
        </Button>
      </div>
    </form>
  );
}
