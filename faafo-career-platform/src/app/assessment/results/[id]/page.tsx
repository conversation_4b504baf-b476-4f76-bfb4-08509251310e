'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import AssessmentResults from '@/components/assessment/AssessmentResults';
import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import Link from 'next/link';

interface AssessmentResultsPageProps {
  params: {
    id: string;
  };
}

export default function AssessmentResultsPage({ params }: AssessmentResultsPageProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login?callbackUrl=' + encodeURIComponent(`/assessment/results/${params.id}`));
    }
  }, [status, router, params.id]);

  if (status === 'loading') {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="text-center mb-8">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please sign in to view your assessment results.</p>
          <Button asChild>
            <Link href={`/login?callbackUrl=${encodeURIComponent(`/assessment/results/${params.id}`)}`}>
              Sign In
            </Link>
          </Button>
        </Card>
      </div>
    );
  }

  if (!params.id) {
    return (
      <div className="container mx-auto py-8">
        <Card className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">Invalid Assessment ID</h2>
          <p className="text-red-600 mb-4">The assessment ID provided is not valid.</p>
          <Button asChild>
            <Link href="/assessment">Take Assessment</Link>
          </Button>
        </Card>
      </div>
    );
  }

  return <AssessmentResults assessmentId={params.id} />;
}
